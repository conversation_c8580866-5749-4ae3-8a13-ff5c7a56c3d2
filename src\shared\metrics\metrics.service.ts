import {
  Counter,
  Histogram,
  register,
  collectDefaultMetrics,
} from 'prom-client';

export class MetricsService {
  private readonly httpRequestsTotal: Counter<string>;
  private readonly httpRequestDurationSeconds: Histogram<string>;
  private readonly httpErrorsTotal: Counter<string>;
  private readonly databaseConnectionsTotal: Counter<string>;
  private readonly redisConnectionsTotal: Counter<string>;

  constructor() {
    // Limpar métricas existentes (útil para testes)
    register.clear();

    // Coletar métricas padrão do Node.js
    collectDefaultMetrics({ register });

    // Contador total de requisições HTTP
    this.httpRequestsTotal = new Counter({
      name: 'http_requests_total',
      help: 'Total number of HTTP requests',
      labelNames: ['method', 'path', 'status_code'],
      registers: [register],
    });

    // Histograma de duração das requisições HTTP
    this.httpRequestDurationSeconds = new Histogram({
      name: 'http_request_duration_seconds',
      help: 'HTTP request duration in seconds',
      labelNames: ['method', 'path', 'status_code'],
      buckets: [0.1, 0.5, 1, 2, 5, 10],
      registers: [register],
    });

    // Contador de erros HTTP
    this.httpErrorsTotal = new Counter({
      name: 'http_errors_total',
      help: 'Total number of HTTP errors',
      labelNames: ['method', 'path', 'status_code'],
      registers: [register],
    });

    // Contador de conexões com banco de dados
    this.databaseConnectionsTotal = new Counter({
      name: 'database_connections_total',
      help: 'Total number of database connections',
      labelNames: ['status'],
      registers: [register],
    });

    // Contador de conexões com Redis
    this.redisConnectionsTotal = new Counter({
      name: 'redis_connections_total',
      help: 'Total number of Redis connections',
      labelNames: ['status'],
      registers: [register],
    });
  }

  /**
   * Registra uma requisição HTTP
   */
  recordRequest(
    method: string,
    path: string,
    statusCode: number,
    duration: number
  ): void {
    const labels = {
      method: method.toUpperCase(),
      path: this.normalizePath(path),
      status_code: statusCode.toString(),
    };

    this.httpRequestsTotal.inc(labels);
    this.httpRequestDurationSeconds.observe(labels, duration);

    if (statusCode >= 400) {
      this.httpErrorsTotal.inc(labels);
    }
  }

  /**
   * Registra uma conexão com banco de dados
   */
  recordDatabaseConnection(status: 'success' | 'error'): void {
    this.databaseConnectionsTotal.inc({ status });
  }

  /**
   * Registra uma conexão com Redis
   */
  recordRedisConnection(status: 'success' | 'error'): void {
    this.redisConnectionsTotal.inc({ status });
  }

  /**
   * Retorna todas as métricas no formato Prometheus
   */
  async getMetrics(): Promise<string> {
    return register.metrics();
  }

  /**
   * Normaliza o path para evitar cardinalidade alta
   */
  private normalizePath(path: string): string {
    // Remove IDs numéricos dos paths
    return path
      .replace(/\/\d+/g, '/:id')
      .replace(/\/[a-f0-9-]{36}/g, '/:uuid')
      .replace(/\/[a-f0-9]{24}/g, '/:objectId');
  }

  /**
   * Limpa todas as métricas (útil para testes)
   */
  static clearMetrics(): void {
    register.clear();
  }
}

// Instância singleton do serviço de métricas
export const metricsService = new MetricsService();
