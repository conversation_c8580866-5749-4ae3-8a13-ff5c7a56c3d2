import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import fp from 'fastify-plugin';
import { metricsService } from './metrics.service';

async function metricsPlugin(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
) {
  // Endpoint para expor métricas do Prometheus
  fastify.get('/metrics', async (request, reply) => {
    const metrics = await metricsService.getMetrics();

    reply
      .header('Content-Type', 'text/plain; version=0.0.4; charset=utf-8')
      .send(metrics);
  });

  // Hook para capturar métricas de todas as requisições
  fastify.addHook('onRequest', async (request, reply) => {
    // Adiciona timestamp de início da requisição
    (request as any).startTime = Date.now();
  });

  fastify.addHook('onResponse', async (request, reply) => {
    const startTime = (request as any).startTime;
    if (startTime) {
      const duration = (Date.now() - startTime) / 1000; // Converter para segundos

      metricsService.recordRequest(
        request.method,
        request.url,
        reply.statusCode,
        duration
      );
    }
  });
}

export default fp(metricsPlugin, {
  name: 'metrics-plugin',
});
